describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/auth/login')
  })

  it('should display login form', () => {
    cy.contains('Sign in to your account').should('be.visible')
    cy.get('input[type="email"]').should('be.visible')
    cy.get('input[type="password"]').should('be.visible')
    cy.get('button[type="submit"]').should('contain', 'Sign in')
  })

  it('should show validation errors for empty form', () => {
    cy.get('button[type="submit"]').click()
    cy.contains('Please enter a valid email address').should('be.visible')
    cy.contains('Password must be at least 6 characters').should('be.visible')
  })

  it('should show validation error for invalid email', () => {
    cy.get('input[type="email"]').type('invalid-email')
    cy.get('button[type="submit"]').click()
    cy.contains('Please enter a valid email address').should('be.visible')
  })

  it('should toggle password visibility', () => {
    cy.get('input[type="password"]').should('have.attr', 'type', 'password')
    cy.get('[data-testid="toggle-password"]').click()
    cy.get('input[type="text"]').should('exist')
    cy.get('[data-testid="toggle-password"]').click()
    cy.get('input[type="password"]').should('exist')
  })

  it('should navigate to register page', () => {
    cy.contains('create a new account').click()
    cy.url().should('include', '/auth/register')
    cy.contains('Create your account').should('be.visible')
  })

  it('should attempt login with valid credentials', () => {
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[type="password"]').type('password123')
    cy.get('button[type="submit"]').click()
    
    // Should show loading state
    cy.contains('Signing in...').should('be.visible')
  })

  it('should redirect authenticated users away from auth pages', () => {
    // Mock authenticated state
    cy.window().then((win) => {
      win.localStorage.setItem('supabase.auth.token', 'mock-token')
    })
    
    cy.visit('/auth/login')
    cy.url().should('include', '/dashboard')
  })
})

describe('Registration', () => {
  beforeEach(() => {
    cy.visit('/auth/register')
  })

  it('should display registration form', () => {
    cy.contains('Create your account').should('be.visible')
    cy.get('input[name="fullName"]').should('be.visible')
    cy.get('input[type="email"]').should('be.visible')
    cy.get('input[name="phone"]').should('be.visible')
    cy.get('input[name="password"]').should('be.visible')
    cy.get('input[name="confirmPassword"]').should('be.visible')
  })

  it('should show validation errors for empty required fields', () => {
    cy.get('button[type="submit"]').click()
    cy.contains('Full name must be at least 2 characters').should('be.visible')
    cy.contains('Please enter a valid email address').should('be.visible')
    cy.contains('Password must be at least 6 characters').should('be.visible')
  })

  it('should show error for password mismatch', () => {
    cy.get('input[name="password"]').type('password123')
    cy.get('input[name="confirmPassword"]').type('different-password')
    cy.get('button[type="submit"]').click()
    cy.contains('Passwords do not match').should('be.visible')
  })

  it('should attempt registration with valid data', () => {
    cy.get('input[name="fullName"]').type('John Doe')
    cy.get('input[type="email"]').type('<EMAIL>')
    cy.get('input[name="phone"]').type('+212 6XX XXX XXX')
    cy.get('input[name="password"]').type('password123')
    cy.get('input[name="confirmPassword"]').type('password123')
    cy.get('button[type="submit"]').click()
    
    // Should show loading state
    cy.contains('Creating account...').should('be.visible')
  })
})
