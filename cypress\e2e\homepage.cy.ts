describe('Homepage', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('should display the homepage correctly', () => {
    // Check if the page redirects to /home
    cy.url().should('include', '/home')
    
    // Check main heading
    cy.contains('Find Your Perfect Property in Morocco').should('be.visible')
    
    // Check hero section elements
    cy.get('[data-testid="hero-section"]').should('be.visible')
    cy.get('[data-testid="search-bar"]').should('be.visible')
    
    // Check navigation
    cy.get('nav').should('be.visible')
    cy.contains('Properties').should('be.visible')
    cy.contains('Services').should('be.visible')
    cy.contains('About').should('be.visible')
    cy.contains('Contact').should('be.visible')
  })

  it('should navigate to properties page', () => {
    cy.contains('Properties').click()
    cy.url().should('include', '/properties')
    cy.contains('Find Your Perfect Property').should('be.visible')
  })

  it('should display featured properties section', () => {
    cy.get('[data-testid="featured-properties"]').should('be.visible')
    cy.contains('Featured Properties').should('be.visible')
    
    // Check if property cards are displayed
    cy.get('[data-testid="property-card"]').should('have.length.at.least', 1)
  })

  it('should display stats section', () => {
    cy.get('[data-testid="stats-section"]').should('be.visible')
    cy.contains('500+').should('be.visible') // Properties Listed
    cy.contains('1000+').should('be.visible') // Happy Clients
  })

  it('should display services section', () => {
    cy.get('[data-testid="services-section"]').should('be.visible')
    cy.contains('Our Services').should('be.visible')
    cy.contains('Property Sales').should('be.visible')
    cy.contains('Property Management').should('be.visible')
  })

  it('should display testimonials section', () => {
    cy.get('[data-testid="testimonials-section"]').should('be.visible')
    cy.contains('What Our Clients Say').should('be.visible')
  })

  it('should have working footer links', () => {
    cy.get('footer').should('be.visible')
    cy.get('footer').contains('Properties').should('be.visible')
    cy.get('footer').contains('Services').should('be.visible')
    cy.get('footer').contains('About Us').should('be.visible')
  })

  it('should be responsive on mobile', () => {
    cy.viewport('iphone-6')
    cy.get('[data-testid="mobile-menu-button"]').should('be.visible')
    cy.get('[data-testid="mobile-menu-button"]').click()
    cy.get('[data-testid="mobile-menu"]').should('be.visible')
  })
})
