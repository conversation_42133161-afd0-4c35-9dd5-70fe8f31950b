import { LoginForm } from '@/components/features/auth/login-form';
import Link from 'next/link';

export default function LoginPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-center text-3xl font-bold tracking-tight text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link
            href="/auth/register"
            className="font-medium text-primary hover:text-primary/80"
          >
            create a new account
          </Link>
        </p>
      </div>

      <LoginForm />

      <div className="text-center">
        <Link
          href="/auth/forgot-password"
          className="text-sm text-primary hover:text-primary/80"
        >
          Forgot your password?
        </Link>
      </div>
    </div>
  );
}
