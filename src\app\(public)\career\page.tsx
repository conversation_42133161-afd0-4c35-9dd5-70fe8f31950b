import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Users, 
  TrendingUp, 
  Heart,
  Briefcase,
  GraduationCap,
  Coffee,
  Shield
} from 'lucide-react';
import Link from 'next/link';

const openPositions = [
  {
    title: 'Senior Real Estate Agent',
    department: 'Sales',
    location: 'Casablanca',
    type: 'Full-time',
    salary: '15,000 - 25,000 MAD',
    description: 'Join our sales team to help clients find their dream properties in Casablanca.',
    requirements: ['3+ years real estate experience', 'Fluent in Arabic, French, English', 'Valid driving license'],
  },
  {
    title: 'Property Manager',
    department: 'Operations',
    location: 'Rabat',
    type: 'Full-time',
    salary: '12,000 - 18,000 MAD',
    description: 'Manage our growing portfolio of rental properties and ensure tenant satisfaction.',
    requirements: ['Property management experience', 'Strong communication skills', 'Problem-solving abilities'],
  },
  {
    title: 'Digital Marketing Specialist',
    department: 'Marketing',
    location: 'Remote',
    type: 'Full-time',
    salary: '10,000 - 15,000 MAD',
    description: 'Drive our digital marketing efforts and enhance our online presence.',
    requirements: ['Digital marketing experience', 'Social media expertise', 'Content creation skills'],
  },
  {
    title: 'Junior Sales Associate',
    department: 'Sales',
    location: 'Marrakech',
    type: 'Full-time',
    salary: '8,000 - 12,000 MAD',
    description: 'Start your real estate career with comprehensive training and mentorship.',
    requirements: ['Bachelor\'s degree', 'Excellent communication', 'Eager to learn'],
  },
];

const benefits = [
  {
    icon: DollarSign,
    title: 'Competitive Salary',
    description: 'Market-leading compensation packages with performance bonuses.',
  },
  {
    icon: TrendingUp,
    title: 'Career Growth',
    description: 'Clear advancement paths and professional development opportunities.',
  },
  {
    icon: GraduationCap,
    title: 'Training & Development',
    description: 'Continuous learning programs and industry certifications.',
  },
  {
    icon: Shield,
    title: 'Health Insurance',
    description: 'Comprehensive health coverage for you and your family.',
  },
  {
    icon: Coffee,
    title: 'Flexible Work',
    description: 'Hybrid work options and flexible scheduling.',
  },
  {
    icon: Heart,
    title: 'Team Culture',
    description: 'Collaborative environment with team building activities.',
  },
];

const values = [
  'Excellence in everything we do',
  'Integrity and transparency',
  'Client-first mentality',
  'Continuous innovation',
  'Teamwork and collaboration',
  'Community involvement',
];

export default function CareerPage() {
  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">Careers</Badge>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Join Our Growing Team
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Build your career with Morocco's leading real estate company. We're looking for 
            passionate professionals to help shape the future of Moroccan real estate.
          </p>
        </div>

        {/* Why Join Us */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Why Work With Us?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We believe our people are our greatest asset. That's why we invest in creating 
              an environment where talent thrives and careers flourish.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <Card key={index} className="text-center p-6">
                  <CardContent className="p-0">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-600">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Open Positions */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Open Positions
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore our current job openings and find the perfect role to advance your career.
            </p>
          </div>

          <div className="space-y-6">
            {openPositions.map((position, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div>
                      <CardTitle className="text-xl mb-2">{position.title}</CardTitle>
                      <div className="flex flex-wrap gap-2 mb-2">
                        <Badge variant="secondary">{position.department}</Badge>
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-1" />
                          {position.location}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-1" />
                          {position.type}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="h-4 w-4 mr-1" />
                          {position.salary}
                        </div>
                      </div>
                    </div>
                    <Button className="mt-4 lg:mt-0">
                      Apply Now
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{position.description}</p>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Requirements:</h4>
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                      {position.requirements.map((req, idx) => (
                        <li key={idx}>{req}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Company Culture */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Our Culture & Values
            </h2>
            <p className="text-gray-600 mb-6">
              At Darden Property & Management, we foster a culture of excellence, 
              innovation, and mutual respect. Our values guide our decisions and 
              shape our interactions with clients and colleagues.
            </p>
            <ul className="space-y-3">
              {values.map((value, index) => (
                <li key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  {value}
                </li>
              ))}
            </ul>
          </div>
          <div className="aspect-[4/3] bg-gray-200 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-400">
              <Users className="h-16 w-16 mx-auto mb-4" />
              <p>Team Culture Image</p>
            </div>
          </div>
        </div>

        {/* Application Process */}
        <div className="bg-gray-50 rounded-2xl p-8 lg:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Application Process
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our streamlined hiring process is designed to find the best talent 
              while providing a positive candidate experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: '01', title: 'Apply Online', desc: 'Submit your application and resume' },
              { step: '02', title: 'Initial Review', desc: 'We review your qualifications' },
              { step: '03', title: 'Interview', desc: 'Meet with our hiring team' },
              { step: '04', title: 'Welcome Aboard', desc: 'Join our team and start growing' },
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mb-4 mx-auto">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {step.desc}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center bg-primary rounded-2xl p-8 lg:p-12 text-white">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Don't See the Right Role?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            We're always looking for talented individuals. Send us your resume 
            and we'll keep you in mind for future opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              <Briefcase className="mr-2 h-5 w-5" />
              Send Your Resume
            </Button>
            <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-primary" asChild>
              <Link href="/contact">Contact HR</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
