import { PropertyDetails } from '@/components/features/properties/property-details';
import { PropertyGallery } from '@/components/features/properties/property-gallery';
import { PropertyInquiryForm } from '@/components/features/properties/property-inquiry-form';
import { SimilarProperties } from '@/components/features/properties/similar-properties';

interface PropertyPageProps {
  params: {
    id: string;
  };
}

export default function PropertyPage({ params }: PropertyPageProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <PropertyGallery propertyId={params.id} />
          <PropertyDetails propertyId={params.id} />
        </div>
        <div className="lg:col-span-1">
          <PropertyInquiryForm propertyId={params.id} />
        </div>
      </div>
      
      <div className="mt-12">
        <SimilarProperties propertyId={params.id} />
      </div>
    </div>
  );
}
