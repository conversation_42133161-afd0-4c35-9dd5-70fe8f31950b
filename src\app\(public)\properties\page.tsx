import { PropertySearch } from '@/components/features/properties/property-search';
import { PropertyGrid } from '@/components/features/properties/property-grid';
import { PropertyFilters } from '@/components/features/properties/property-filters';

export default function PropertiesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Find Your Perfect Property
        </h1>
        <p className="text-gray-600 max-w-2xl">
          Discover exceptional properties across Morocco. From luxury villas in Casablanca 
          to traditional riads in Marrakech, find your dream home with us.
        </p>
      </div>
      
      <PropertySearch />
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mt-8">
        <div className="lg:col-span-1">
          <PropertyFilters />
        </div>
        <div className="lg:col-span-3">
          <PropertyGrid />
        </div>
      </div>
    </div>
  );
}
