import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Home, 
  Key, 
  Calculator, 
  Users, 
  Shield, 
  Headphones,
  CheckCircle,
  Star,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

const services = [
  {
    icon: Home,
    title: 'Property Sales',
    description: 'Expert guidance in buying and selling residential and commercial properties across Morocco.',
    features: ['Market Analysis', 'Property Valuation', 'Negotiation Support', 'Legal Assistance'],
    price: 'Commission-based',
  },
  {
    icon: Key,
    title: 'Property Management',
    description: 'Comprehensive management services for property owners and investors.',
    features: ['Tenant Screening', 'Rent Collection', 'Maintenance Coordination', 'Financial Reporting'],
    price: 'From 5% monthly rent',
  },
  {
    icon: Calculator,
    title: 'Property Valuation',
    description: 'Professional property appraisal and market analysis services.',
    features: ['Market Research', 'Comparative Analysis', 'Investment Potential', 'Detailed Reports'],
    price: 'From 2,000 MAD',
  },
  {
    icon: Users,
    title: 'Investment Consulting',
    description: 'Strategic advice for real estate investment opportunities in Morocco.',
    features: ['Portfolio Analysis', 'ROI Calculations', 'Risk Assessment', 'Market Trends'],
    price: 'From 5,000 MAD',
  },
  {
    icon: Shield,
    title: 'Legal Support',
    description: 'Complete legal assistance for property transactions and documentation.',
    features: ['Contract Review', 'Due Diligence', 'Title Verification', 'Registration Support'],
    price: 'From 3,000 MAD',
  },
  {
    icon: Headphones,
    title: '24/7 Support',
    description: 'Round-the-clock customer support for all your real estate needs.',
    features: ['Phone Support', 'Email Assistance', 'Emergency Response', 'Multilingual Service'],
    price: 'Included',
  },
];

const processSteps = [
  {
    step: '01',
    title: 'Initial Consultation',
    description: 'We discuss your needs and requirements to understand your goals.',
  },
  {
    step: '02',
    title: 'Property Search/Analysis',
    description: 'Our experts search for properties or analyze your current portfolio.',
  },
  {
    step: '03',
    title: 'Negotiation & Documentation',
    description: 'We handle negotiations and ensure all paperwork is completed properly.',
  },
  {
    step: '04',
    title: 'Closing & Follow-up',
    description: 'We guide you through closing and provide ongoing support as needed.',
  },
];

export default function ServicesPage() {
  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">Our Services</Badge>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Comprehensive Real Estate Services
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From property sales to management, we provide end-to-end real estate solutions 
            tailored to the Moroccan market. Your success is our priority.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <Icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{service.title}</CardTitle>
                  <div className="text-sm font-medium text-primary">{service.price}</div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-6">{service.description}</p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Process Section */}
        <div className="bg-gray-50 rounded-2xl p-8 lg:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Process
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We follow a proven process to ensure the best outcomes for our clients. 
              Every step is designed to provide transparency and peace of mind.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mb-4 mx-auto">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-primary rounded-2xl p-8 lg:p-12 text-white">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Contact us today for a free consultation and discover how we can help 
            you achieve your real estate goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="/contact">Get Free Consultation</Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-primary" asChild>
              <Link href="/properties">Browse Properties</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
