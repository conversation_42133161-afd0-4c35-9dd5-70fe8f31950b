@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Darden PM Color Scheme - White, <PERSON>, Green */
  --background: #ffffff;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #16a34a; /* Green accent */
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5; /* Light grey */
  --secondary-foreground: #1a1a1a;
  --muted: #f5f5f5;
  --muted-foreground: #6b7280;
  --accent: #f5f5f5;
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #16a34a;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
    --card: #1a1a1a;
    --card-foreground: #ffffff;
    --popover: #1a1a1a;
    --popover-foreground: #ffffff;
    --primary: #22c55e;
    --primary-foreground: #000000;
    --secondary: #262626;
    --secondary-foreground: #ffffff;
    --muted: #262626;
    --muted-foreground: #a1a1aa;
    --accent: #262626;
    --accent-foreground: #ffffff;
    --border: #404040;
    --input: #404040;
    --ring: #22c55e;
  }
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
