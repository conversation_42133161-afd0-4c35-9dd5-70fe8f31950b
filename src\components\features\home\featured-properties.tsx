'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bed, Bath, Square, MapPin, Eye } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Mock data - will be replaced with real data from Supabase
const featuredProperties = [
  {
    id: '1',
    title: 'Luxury Villa in Casablanca',
    price: 2500000,
    currency: 'MAD',
    location: 'Anfa District, Casablanca',
    bedrooms: 5,
    bathrooms: 4,
    area: 450,
    image: '/images/villa-placeholder.jpg',
    status: 'available',
    type: 'villa',
  },
  {
    id: '2',
    title: 'Modern Apartment in Rabat',
    price: 850000,
    currency: 'MAD',
    location: 'Agdal, Rabat',
    bedrooms: 3,
    bathrooms: 2,
    area: 120,
    image: '/images/apartment-placeholder.jpg',
    status: 'available',
    type: 'apartment',
  },
  {
    id: '3',
    title: 'Traditional Riad in Marrakech',
    price: 1200000,
    currency: 'MAD',
    location: 'Medina, Marrakech',
    bedrooms: 4,
    bathrooms: 3,
    area: 200,
    image: '/images/riad-placeholder.jpg',
    status: 'available',
    type: 'house',
  },
];

export function FeaturedProperties() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Featured Properties
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover our handpicked selection of premium properties across Morocco's 
            most sought-after locations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredProperties.map((property) => (
            <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <div className="aspect-[4/3] bg-gray-200 relative">
                  <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                    <Eye className="h-12 w-12" />
                  </div>
                </div>
                <div className="absolute top-4 left-4">
                  <Badge variant="success">
                    {property.status === 'available' ? 'Available' : property.status}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary">
                    {property.type}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="mb-4">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {property.title}
                  </h3>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm">{property.location}</span>
                  </div>
                  <div className="text-2xl font-bold text-primary">
                    {property.price.toLocaleString()} {property.currency}
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Bed className="h-4 w-4 mr-1" />
                    <span>{property.bedrooms} beds</span>
                  </div>
                  <div className="flex items-center">
                    <Bath className="h-4 w-4 mr-1" />
                    <span>{property.bathrooms} baths</span>
                  </div>
                  <div className="flex items-center">
                    <Square className="h-4 w-4 mr-1" />
                    <span>{property.area} m²</span>
                  </div>
                </div>

                <Button asChild className="w-full">
                  <Link href={`/properties/${property.id}`}>
                    View Details
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button size="lg" variant="outline" asChild>
            <Link href="/properties">View All Properties</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
