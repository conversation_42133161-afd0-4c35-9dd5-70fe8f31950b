import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Home, Key, Calculator, Users, Shield, Headphones } from 'lucide-react';
import Link from 'next/link';

const services = [
  {
    icon: Home,
    title: 'Property Sales',
    description: 'Expert guidance in buying and selling residential and commercial properties.',
  },
  {
    icon: Key,
    title: 'Property Management',
    description: 'Comprehensive management services for property owners and investors.',
  },
  {
    icon: Calculator,
    title: 'Property Valuation',
    description: 'Professional property appraisal and market analysis services.',
  },
  {
    icon: Users,
    title: 'Investment Consulting',
    description: 'Strategic advice for real estate investment opportunities.',
  },
  {
    icon: Shield,
    title: 'Legal Support',
    description: 'Complete legal assistance for property transactions and documentation.',
  },
  {
    icon: Headphones,
    title: '24/7 Support',
    description: 'Round-the-clock customer support for all your real estate needs.',
  },
];

export function ServicesSection() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Our Services
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Comprehensive real estate services tailored to meet your needs. 
            From property sales to management, we've got you covered.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 mx-auto">
                    <Icon className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{service.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center">
          <Button size="lg" asChild>
            <Link href="/services">Learn More About Our Services</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
