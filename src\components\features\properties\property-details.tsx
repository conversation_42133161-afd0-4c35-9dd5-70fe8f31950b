'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Bed, Bath, Square, MapPin, Calendar, User, Check } from 'lucide-react';

interface PropertyDetailsProps {
  propertyId: string;
}

export function PropertyDetails({ propertyId }: PropertyDetailsProps) {
  // Mock data - will be replaced with real data from Supabase
  const property = {
    id: propertyId,
    title: 'Luxury Villa in Casablanca',
    description: 'Beautiful modern villa with stunning architecture, located in the prestigious Anfa district. Features include a private pool, garden, and panoramic city views. This exceptional property offers the perfect blend of luxury and comfort, with high-end finishes throughout.',
    price: 2500000,
    currency: 'MAD',
    location: 'Anfa District, Casablanca',
    address: '123 Avenue Mohammed V, Anfa, Casablanca',
    bedrooms: 5,
    bathrooms: 4,
    area: 450,
    type: 'villa',
    status: 'available',
    features: [
      'Swimming Pool',
      'Garden',
      'Garage',
      'Security System',
      'City View',
      'Air Conditioning',
      'Fireplace',
      'Balcony',
    ],
    createdAt: '2024-01-15',
    agent: {
      name: '<PERSON>',
      phone: '+212 6XX XXX XXX',
      email: '<EMAIL>',
    },
  };

  return (
    <div className="space-y-6 mt-6">
      {/* Basic Info */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="success">{property.status}</Badge>
          <Badge variant="secondary">{property.type}</Badge>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {property.title}
        </h1>
        <div className="flex items-center text-gray-600 mb-4">
          <MapPin className="h-5 w-5 mr-2" />
          <span>{property.address}</span>
        </div>
        <div className="text-3xl font-bold text-primary mb-6">
          {property.price.toLocaleString()} {property.currency}
        </div>
      </div>

      {/* Key Features */}
      <Card>
        <CardHeader>
          <CardTitle>Property Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <Bed className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{property.bedrooms}</div>
              <div className="text-sm text-gray-600">Bedrooms</div>
            </div>
            <div className="text-center">
              <Bath className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{property.bathrooms}</div>
              <div className="text-sm text-gray-600">Bathrooms</div>
            </div>
            <div className="text-center">
              <Square className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{property.area}</div>
              <div className="text-sm text-gray-600">m² Area</div>
            </div>
            <div className="text-center">
              <Calendar className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">2024</div>
              <div className="text-sm text-gray-600">Year Built</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">
            {property.description}
          </p>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Features & Amenities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {property.features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-primary" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Location */}
      <Card>
        <CardHeader>
          <CardTitle>Location</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center text-gray-400 mb-4">
            <div className="text-center">
              <MapPin className="h-12 w-12 mx-auto mb-2" />
              <p>Interactive Map</p>
            </div>
          </div>
          <p className="text-gray-600">
            Located in the prestigious Anfa district of Casablanca, this property 
            offers easy access to shopping centers, restaurants, and business districts.
          </p>
        </CardContent>
      </Card>

      {/* Agent Info */}
      <Card>
        <CardHeader>
          <CardTitle>Listed by</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div>
              <div className="font-semibold">{property.agent.name}</div>
              <div className="text-sm text-gray-600">{property.agent.phone}</div>
              <div className="text-sm text-gray-600">{property.agent.email}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
