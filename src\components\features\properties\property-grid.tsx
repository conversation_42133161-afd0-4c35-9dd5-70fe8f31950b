'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bed, Bath, Square, MapPin, Eye, Heart } from 'lucide-react';
import Link from 'next/link';

// Mock data - will be replaced with real data from Supabase
const properties = [
  {
    id: '1',
    title: 'Luxury Villa in Casablanca',
    price: 2500000,
    currency: 'MAD',
    location: 'Anfa District, Casablanca',
    bedrooms: 5,
    bathrooms: 4,
    area: 450,
    image: '/images/villa-placeholder.jpg',
    status: 'available',
    type: 'villa',
  },
  {
    id: '2',
    title: 'Modern Apartment in Rabat',
    price: 850000,
    currency: 'MAD',
    location: 'Agdal, Rabat',
    bedrooms: 3,
    bathrooms: 2,
    area: 120,
    image: '/images/apartment-placeholder.jpg',
    status: 'available',
    type: 'apartment',
  },
  // Add more mock properties...
];

export function PropertyGrid() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {properties.length} properties
        </p>
        <select className="border rounded-md px-3 py-2">
          <option>Sort by: Latest</option>
          <option>Price: Low to High</option>
          <option>Price: High to Low</option>
          <option>Area: Largest First</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {properties.map((property) => (
          <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <div className="aspect-[4/3] bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <Eye className="h-12 w-12" />
                </div>
              </div>
              <div className="absolute top-4 left-4">
                <Badge variant="success">
                  {property.status === 'available' ? 'Available' : property.status}
                </Badge>
              </div>
              <div className="absolute top-4 right-4 flex gap-2">
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {property.title}
                </h3>
                <div className="flex items-center text-gray-600 mb-2">
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="text-sm line-clamp-1">{property.location}</span>
                </div>
                <div className="text-xl font-bold text-primary">
                  {property.price.toLocaleString()} {property.currency}
                </div>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <div className="flex items-center">
                  <Bed className="h-4 w-4 mr-1" />
                  <span>{property.bedrooms}</span>
                </div>
                <div className="flex items-center">
                  <Bath className="h-4 w-4 mr-1" />
                  <span>{property.bathrooms}</span>
                </div>
                <div className="flex items-center">
                  <Square className="h-4 w-4 mr-1" />
                  <span>{property.area} m²</span>
                </div>
              </div>

              <Button asChild className="w-full">
                <Link href={`/properties/${property.id}`}>
                  View Details
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-8">
        <div className="flex items-center space-x-2">
          <Button variant="outline" disabled>Previous</Button>
          <Button variant="outline">1</Button>
          <Button>2</Button>
          <Button variant="outline">3</Button>
          <Button variant="outline">Next</Button>
        </div>
      </div>
    </div>
  );
}
