'use client';

import { useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';

interface AuthUser extends User {
  role?: 'admin' | 'sales_person' | 'user';
  full_name?: string;
  phone?: string;
}

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        // Fetch user profile data
        const { data: profile } = await supabase
          .from('users')
          .select('role, full_name, phone')
          .eq('id', session.user.id)
          .single();

        setUser({
          ...session.user,
          ...profile,
        });
      }
      
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          // Fetch user profile data
          const { data: profile } = await supabase
            .from('users')
            .select('role, full_name, phone')
            .eq('id', session.user.id)
            .single();

          setUser({
            ...session.user,
            ...profile,
          });
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
  };

  const isAdmin = user?.role === 'admin';
  const isSalesPerson = user?.role === 'sales_person';
  const isAdminOrSales = isAdmin || isSalesPerson;

  return {
    user,
    loading,
    signOut,
    isAdmin,
    isSalesPerson,
    isAdminOrSales,
    isAuthenticated: !!user,
  };
}
